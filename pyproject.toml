[tool.poetry]
name = "hoplogic"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
django = "^5.2.4"
django-rest-framework = "^0.1.0"
langchain = "^0.3.26"
langgraph = "^0.5.2"
langchain-ollama = "^0.3.4"
langchain-openai = "^0.3.27"
pypdf = "^5.7.0"
langchain-community = "^0.3.27"
requests = "^2.32.4"
beautifulsoup4 = "^4.13.4"
pydantic = "^2.11.7"
lxml = "^6.0.0"
psycopg2-binary = "^2.9.10"
djangorestframework = "^3.16.0"


[tool.poetry.group.dev.dependencies]
ruff = "^0.12.2"
isort = "^6.0.1"
mypy = "^1.16.1"
pytest = "^8.4.1"
pytest-django = "^4.11.1"
black = "^25.1.0"
pytest-cov = "^6.2.1"
pytest-asyncio = "^1.1.0"
django-migration-testcase = "^0.0.15"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "hoplogic.test_settings"
python_files = ["tests.py", "test_*.py", "*_tests.py"]
addopts = "--tb=short --strict-markers --reuse-db"
testpaths = ["tests"]

[tool.isort]
profile = "black"
known_first_party = ["hoplogic", "core"]
line_length = 88
