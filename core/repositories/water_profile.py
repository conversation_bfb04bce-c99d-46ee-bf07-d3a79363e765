"""
Water profile repository for the hoplogic application.

This module contains the repository class for WaterProfile model operations.
"""

from ..di import injectable
from ..models import WaterProfile
from .base import BaseRepository


@injectable
class WaterProfileRepository(BaseRepository[WaterProfile]):
    """Repository for WaterProfile model operations."""

    def __init__(self):
        super().__init__(WaterProfile)

    def search_by_name(self, name_query: str) -> list[WaterProfile]:
        """Search water profiles by name (case-insensitive partial match)."""
        return self.filter(name__icontains=name_query)

    def get_by_name(self, name: str) -> WaterProfile | None:
        """Get a water profile by exact name."""
        try:
            return self.model_class.objects.get(name=name)
        except self.model_class.DoesNotExist:
            return None
