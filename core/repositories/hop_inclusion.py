"""
Hop inclusion repositories for the hoplogic application.

This module contains repository classes for all hop inclusion model operations.
"""

from ..di import injectable
from ..models import (
    BoilHopInclusion,
    DryHopInclusion,
    FirstWortHopInclusion,
    FlameoutHopInclusion,
    WhirlpoolHopInclusion,
)
from .base import BaseRepository


@injectable
class BoilHopInclusionRepository(BaseRepository[BoilHopInclusion]):
    """Repository for BoilHopInclusion model operations."""

    def __init__(self):
        super().__init__(BoilHopInclusion)

    def get_by_recipe(self, recipe_id: str) -> list[BoilHopInclusion]:
        """Get all boil hop inclusions for a recipe."""
        return self.filter(recipe_id=recipe_id)

    def get_by_recipe_with_hops(self, recipe_id: str) -> list[BoilHopInclusion]:
        """Get boil hop inclusions for a recipe with hop data prefetched."""
        return list(
            self.model_class.objects.select_related("hop")
            .filter(recipe_id=recipe_id)
            .order_by("-time_minutes")
        )

    def exists_for_recipe_and_hop(self, recipe_id: str, hop_id: str) -> bool:
        """Check if a boil hop inclusion exists for a recipe and hop."""
        return self.exists(recipe_id=recipe_id, hop_id=hop_id)


@injectable
class FirstWortHopInclusionRepository(BaseRepository[FirstWortHopInclusion]):
    """Repository for FirstWortHopInclusion model operations."""

    def __init__(self):
        super().__init__(FirstWortHopInclusion)

    def get_by_recipe(self, recipe_id: str) -> list[FirstWortHopInclusion]:
        """Get all first wort hop inclusions for a recipe."""
        return self.filter(recipe_id=recipe_id)

    def get_by_recipe_with_hops(self, recipe_id: str) -> list[FirstWortHopInclusion]:
        """Get first wort hop inclusions for a recipe with hop data prefetched."""
        return list(
            self.model_class.objects.select_related("hop").filter(recipe_id=recipe_id)
        )

    def exists_for_recipe_and_hop(self, recipe_id: str, hop_id: str) -> bool:
        """Check if a first wort hop inclusion exists for a recipe and hop."""
        return self.exists(recipe_id=recipe_id, hop_id=hop_id)


@injectable
class FlameoutHopInclusionRepository(BaseRepository[FlameoutHopInclusion]):
    """Repository for FlameoutHopInclusion model operations."""

    def __init__(self):
        super().__init__(FlameoutHopInclusion)

    def get_by_recipe(self, recipe_id: str) -> list[FlameoutHopInclusion]:
        """Get all flameout hop inclusions for a recipe."""
        return self.filter(recipe_id=recipe_id)

    def get_by_recipe_with_hops(self, recipe_id: str) -> list[FlameoutHopInclusion]:
        """Get flameout hop inclusions for a recipe with hop data prefetched."""
        return list(
            self.model_class.objects.select_related("hop").filter(recipe_id=recipe_id)
        )

    def exists_for_recipe_and_hop(self, recipe_id: str, hop_id: str) -> bool:
        """Check if a flameout hop inclusion exists for a recipe and hop."""
        return self.exists(recipe_id=recipe_id, hop_id=hop_id)


@injectable
class WhirlpoolHopInclusionRepository(BaseRepository[WhirlpoolHopInclusion]):
    """Repository for WhirlpoolHopInclusion model operations."""

    def __init__(self):
        super().__init__(WhirlpoolHopInclusion)

    def get_by_recipe(self, recipe_id: str) -> list[WhirlpoolHopInclusion]:
        """Get all whirlpool hop inclusions for a recipe."""
        return self.filter(recipe_id=recipe_id)

    def get_by_recipe_with_hops(self, recipe_id: str) -> list[WhirlpoolHopInclusion]:
        """Get whirlpool hop inclusions for a recipe with hop data prefetched."""
        return list(
            self.model_class.objects.select_related("hop").filter(recipe_id=recipe_id)
        )

    def exists_for_recipe_and_hop(self, recipe_id: str, hop_id: str) -> bool:
        """Check if a whirlpool hop inclusion exists for a recipe and hop."""
        return self.exists(recipe_id=recipe_id, hop_id=hop_id)


@injectable
class DryHopInclusionRepository(BaseRepository[DryHopInclusion]):
    """Repository for DryHopInclusion model operations."""

    def __init__(self):
        super().__init__(DryHopInclusion)

    def get_by_recipe(self, recipe_id: str) -> list[DryHopInclusion]:
        """Get all dry hop inclusions for a recipe."""
        return self.filter(recipe_id=recipe_id)

    def get_by_recipe_with_hops(self, recipe_id: str) -> list[DryHopInclusion]:
        """Get dry hop inclusions for a recipe with hop data prefetched."""
        return list(
            self.model_class.objects.select_related("hop").filter(recipe_id=recipe_id)
        )

    def exists_for_recipe_and_hop(self, recipe_id: str, hop_id: str) -> bool:
        """Check if a dry hop inclusion exists for a recipe and hop."""
        return self.exists(recipe_id=recipe_id, hop_id=hop_id)


# Type alias for any hop inclusion type
HopInclusionType = (
    BoilHopInclusion
    | FirstWortHopInclusion
    | FlameoutHopInclusion
    | WhirlpoolHopInclusion
    | DryHopInclusion
)
