from abc import ABC, abstractmethod

from ..di import injectable
from ..models import BeerStyle
from .base import DjangoDatabaseModelRepositoryMixin


class BeerStyleRepository(ABC):
    @abstractmethod
    def search_freetext(self, name_query: str) -> list[BeerStyle]: ...

    @abstractmethod
    def get_default_style(self) -> BeerStyle: ...


@injectable(interface=BeerStyleRepository)
class DjangoDatabaseBeerStyleRepository(
    BeerStyleRepository,
    DjangoDatabaseModelRepositoryMixin[BeerStyle],
):
    """Repository for BeerStyle model operations."""

    model_class = BeerStyle

    def search_freetext(self, name_query: str) -> list[BeerStyle]:
        """Search beer styles by name (case-insensitive partial match)."""
        # TODO: Do this as a vector embeddings search instead of a text search
        return self.filter(name__icontains=name_query)

    def get_default_style(self) -> BeerStyle:
        return self.filter(name="Custom Style").pop()
