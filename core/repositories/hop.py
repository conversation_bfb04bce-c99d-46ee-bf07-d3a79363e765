"""
Hop repository for the hoplogic application.

This module contains the repository class for Hop model operations.
"""

from ..di import injectable
from ..models import Hop
from .base import BaseRepository


@injectable
class HopRepository(BaseRepository[Hop]):
    """Repository for Hop model operations."""

    def __init__(self):
        super().__init__(Hop)

    def get_aroma_hops(self) -> list[Hop]:
        """Get all hops suitable for aroma."""
        return self.filter(aroma=True)

    def get_bittering_hops(self) -> list[Hop]:
        """Get all hops suitable for bittering."""
        return self.filter(bittering=True)

    def get_dual_purpose_hops(self) -> list[Hop]:
        """Get hops suitable for both aroma and bittering."""
        return self.filter(aroma=True, bittering=True)

    def search_by_name(self, name_query: str) -> list[Hop]:
        """Search hops by name (case-insensitive partial match)."""
        return self.filter(name__icontains=name_query)

    def get_by_country(self, country: str) -> list[Hop]:
        """Get hops from a specific country."""
        return self.filter(country_of_origin__iexact=country)

    def get_by_alpha_acid_range(self, min_alpha: float, max_alpha: float) -> list[Hop]:
        """Get hops within a specific alpha acid range."""
        return self.filter(alpha_acid__gte=min_alpha, alpha_acid__lte=max_alpha)

    def get_high_alpha_hops(self, min_alpha: float = 10.0) -> list[Hop]:
        """Get high alpha acid hops (default: >= 10%)."""
        return self.filter(alpha_acid__gte=min_alpha)

    def get_low_alpha_hops(self, max_alpha: float = 6.0) -> list[Hop]:
        """Get low alpha acid hops (default: <= 6%)."""
        return self.filter(alpha_acid__lte=max_alpha)
