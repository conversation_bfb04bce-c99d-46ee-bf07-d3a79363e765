"""
Base repository class for the hoplogic application.

This module contains the base repository class that provides common
database operations for all model repositories.
"""

from typing import Generic, TypeVar, Type

from django.core.exceptions import ObjectDoesNotExist
from django.db import models


ModelType = TypeVar("ModelType", bound=models.Model)


class DjangoDatabaseModelRepositoryMixin(Generic[ModelType]):
    """
    Base repository class that provides common database operations.

    This class encapsulates basic CRUD operations and can be extended
    by specific model repositories.
    """

    model_class: Type[ModelType]

    def get_by_id(self, id: str) -> ModelType | None:
        """Get a model instance by ID."""
        try:
            return self.get_by_id_or_raise(id)
        except ObjectDoesNotExist:
            return None

    def get_by_id_or_raise(self, id: str) -> ModelType:
        """Get a model instance by ID or raise exception if not found."""
        return self.model_class.objects.get(id=id)

    def get_all(self) -> list[ModelType]:
        """Get all model instances."""
        return list(self.model_class.objects.all())

    def filter(self, **kwargs) -> list[ModelType]:
        """Filter model instances by given criteria."""
        return list(self.model_class.objects.filter(**kwargs))
