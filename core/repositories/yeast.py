"""
Yeast repository for the hoplogic application.

This module contains the repository class for Yeast model operations.
"""

from ..di import injectable
from ..models import Yeast, YeastForm, YeastInclusion, YeastType
from .base import BaseRepository


@injectable
class YeastRepository(BaseRepository[Yeast]):
    """Repository for Yeast model operations."""

    def __init__(self):
        super().__init__(Yeast)

    def get_by_type(self, yeast_type: YeastType) -> list[Yeast]:
        """Get all yeasts of a specific type."""
        return self.filter(yeast_type=yeast_type)

    def get_ale_yeasts(self) -> list[Yeast]:
        """Get all ale yeasts."""
        return self.get_by_type(YeastType.ALE)

    def get_lager_yeasts(self) -> list[Yeast]:
        """Get all lager yeasts."""
        return self.get_by_type(YeastType.LAGER)

    def get_by_form(self, yeast_form: YeastForm) -> list[Yeast]:
        """Get yeasts by form (dry, liquid, etc.)."""
        return self.filter(yeast_form=yeast_form)

    def get_by_laboratory(self, laboratory: str) -> list[Yeast]:
        """Get yeasts from a specific laboratory."""
        return self.filter(laboratory__iexact=laboratory)

    def search_by_name(self, name_query: str) -> list[Yeast]:
        """Search yeasts by name (case-insensitive partial match)."""
        return self.filter(name__icontains=name_query)

    def get_by_temperature_range(self, min_temp: float, max_temp: float) -> list[Yeast]:
        """Get yeasts suitable for a temperature range."""
        return self.filter(
            min_temperature_fahrenheit__lte=max_temp,
            max_temperature_fahrenheit__gte=min_temp,
        )

    def get_by_attenuation_range(
        self, min_attenuation: float, max_attenuation: float
    ) -> list[Yeast]:
        """Get yeasts within an attenuation range."""
        return self.filter(
            attenuation_percent__gte=min_attenuation,
            attenuation_percent__lte=max_attenuation,
        )

    def get_high_attenuation_yeasts(self, min_attenuation: float = 80.0) -> list[Yeast]:
        """Get high attenuation yeasts (default: >= 80%)."""
        return self.filter(attenuation_percent__gte=min_attenuation)


@injectable
class YeastInclusionRepository(BaseRepository[YeastInclusion]):
    """Repository for YeastInclusion model operations."""

    def __init__(self):
        super().__init__(YeastInclusion)

    def get_by_recipe(self, recipe_id: str) -> list[YeastInclusion]:
        """Get all yeast inclusions for a recipe."""
        return self.filter(recipe_id=recipe_id)

    def get_by_recipe_with_yeast(self, recipe_id: str) -> list[YeastInclusion]:
        """Get yeast inclusions for a recipe with yeast data prefetched."""
        return list(
            self.model_class.objects.select_related("yeast").filter(recipe_id=recipe_id)
        )

    def get_by_recipe_and_yeast(
        self, recipe_id: str, yeast_id: str
    ) -> YeastInclusion | None:
        """Get a specific yeast inclusion by recipe and yeast."""
        try:
            return self.model_class.objects.get(recipe_id=recipe_id, yeast_id=yeast_id)
        except self.model_class.DoesNotExist:
            return None

    def exists_for_recipe_and_yeast(self, recipe_id: str, yeast_id: str) -> bool:
        """Check if a yeast inclusion exists for a recipe and yeast."""
        return self.exists(recipe_id=recipe_id, yeast_id=yeast_id)
