"""
Mash step repository for the hoplogic application.

This module contains the repository class for MashStep model operations.
"""

from django.db import models

from ..di import injectable
from ..models import MashStep
from .base import BaseRepository


@injectable
class MashStepRepository(BaseRepository[MashStep]):
    """Repository for MashStep model operations."""

    def __init__(self):
        super().__init__(MashStep)

    def get_by_recipe(self, recipe_id: str) -> list[MashStep]:
        """Get all mash steps for a recipe, ordered by step_order."""
        return list(
            self.model_class.objects.filter(recipe_id=recipe_id).order_by("step_order")
        )
