"""
Recipe repository for the hoplogic application.

This module contains the repository class for Recipe model operations.
"""

from ..di import injectable
from ..models import Recipe
from .base import BaseRepository


@injectable
class RecipeRepository(BaseRepository[Recipe]):
    """Repository for Recipe model operations."""

    def __init__(self):
        super().__init__(Recipe)

    def get_by_user(self, user_id: str) -> list[Recipe]:
        """Get all recipes for a specific user."""
        return self.filter(user_id=user_id)

    def get_by_name(self, name: str) -> Recipe | None:
        """Get a recipe by name."""
        try:
            return self.model_class.objects.get(name=name)
        except self.model_class.DoesNotExist:
            return None

    def search_by_name(self, name_query: str) -> list[Recipe]:
        """Search recipes by name (case-insensitive partial match)."""
        return self.filter(name__icontains=name_query)

    def get_with_fermentables(self, recipe_id: str) -> Recipe | None:
        """Get a recipe with fermentable inclusions prefetched."""
        try:
            return self.model_class.objects.prefetch_related(
                "fermentableinclusion_set__fermentable"
            ).get(id=recipe_id)
        except self.model_class.DoesNotExist:
            return None

    def get_with_all_inclusions(self, recipe_id: str) -> Recipe | None:
        """Get a recipe with all inclusions prefetched."""
        try:
            return self.model_class.objects.prefetch_related(
                "fermentableinclusion_set__fermentable",
                "boilhopinclusion_set__hop",
                "firstworthopinclusion_set__hop",
                "whirlpoolhopinclusion_set__hop",
                "dryhopinclusion_set__hop",
                "yeastinclusion_set__yeast",
                "mashstep_set",
                "fermentationphase_set",
            ).get(id=recipe_id)
        except self.model_class.DoesNotExist:
            return None
