"""
Fermentable inclusion repository for the hoplogic application.

This module contains the repository class for FermentableInclusion model operations.
"""

from ..di import injectable
from ..models import FermentableInclusion
from .base import BaseRepository


@injectable
class FermentableInclusionRepository(BaseRepository[FermentableInclusion]):
    """Repository for FermentableInclusion model operations."""

    def __init__(self):
        super().__init__(FermentableInclusion)

    def get_by_recipe(self, recipe_id: str) -> list[FermentableInclusion]:
        """Get all fermentable inclusions for a recipe."""
        return self.filter(recipe_id=recipe_id)

    def get_by_recipe_with_fermentables(
        self, recipe_id: str
    ) -> list[FermentableInclusion]:
        """Get fermentable inclusions for a recipe with fermentable data prefetched."""
        return list(
            self.model_class.objects.select_related("fermentable").filter(
                recipe_id=recipe_id
            )
        )

    def get_by_recipe_and_fermentable(
        self, recipe_id: str, fermentable_id: str
    ) -> FermentableInclusion | None:
        """Get a specific fermentable inclusion by recipe and fermentable."""
        try:
            return self.model_class.objects.get(
                recipe_id=recipe_id, fermentable_id=fermentable_id
            )
        except self.model_class.DoesNotExist:
            return None

    def exists_for_recipe_and_fermentable(
        self, recipe_id: str, fermentable_id: str
    ) -> bool:
        """Check if a fermentable inclusion exists for a recipe and fermentable."""
        return self.exists(recipe_id=recipe_id, fermentable_id=fermentable_id)

    def get_base_malt_inclusions(self, recipe_id: str) -> list[FermentableInclusion]:
        """Get base malt inclusions for a recipe."""
        return list(
            self.model_class.objects.select_related("fermentable").filter(
                recipe_id=recipe_id, fermentable__fermentable_type="BASE"
            )
        )

    def get_specialty_inclusions(self, recipe_id: str) -> list[FermentableInclusion]:
        """Get specialty malt inclusions for a recipe."""
        return list(
            self.model_class.objects.select_related("fermentable").filter(
                recipe_id=recipe_id,
                fermentable__fermentable_type__in=["SPECIALTY", "CRYSTAL", "ROASTED"],
            )
        )
