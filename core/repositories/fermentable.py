"""
Fermentable repository for the hoplogic application.

This module contains the repository class for Fermentable model operations.
"""

from ..di import injectable
from ..models import Fermentable, FermentableType
from .base import BaseRepository


@injectable
class FermentableRepository(BaseRepository[Fermentable]):
    """Repository for Fermentable model operations."""

    def __init__(self):
        super().__init__(Fermentable)

    def get_by_type(self, fermentable_type: FermentableType) -> list[Fermentable]:
        """Get all fermentables of a specific type."""
        return self.filter(fermentable_type=fermentable_type)

    def get_base_malts(self) -> list[Fermentable]:
        """Get all base malt fermentables."""
        return self.get_by_type(FermentableType.BASE_MALT)

    def get_specialty_malts(self) -> list[Fermentable]:
        """Get all specialty malt fermentables."""
        return self.filter(
            fermentable_type__in=[
                FermentableType.SPECIALTY_MALT,
                FermentableType.CRYSTAL_CARAMEL,
                FermentableType.ROASTED,
            ]
        )

    def search_by_name(self, name_query: str) -> list[Fermentable]:
        """Search fermentables by name (case-insensitive partial match)."""
        return self.filter(name__icontains=name_query)

    def get_by_country(self, country: str) -> list[Fermentable]:
        """Get fermentables from a specific country."""
        return self.filter(country_of_origin__iexact=country)

    def get_requiring_mashing(self) -> list[Fermentable]:
        """Get fermentables that require mashing."""
        return self.filter(requires_mashing=True)

    def get_extracts(self) -> list[Fermentable]:
        """Get extract fermentables (don't require mashing)."""
        return self.filter(requires_mashing=False)
