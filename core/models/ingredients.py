from django.db import models

from .base import BaseModel


class FermentableType(models.TextChoices):
    BASE_MALT = "BASE", "Base Malt"
    SPECIALTY_MALT = "SPECIALTY", "Specialty Malt"
    CRYSTAL_CARAMEL = "CRYSTAL", "Crystal/Caramel Malt"
    ROASTED = "ROASTED", "Roasted Malt"
    ADJUNCT = "ADJUNCT", "Adjunct"
    EXTRACT = "EXTRACT", "Malt Extract"


class Ingredient(BaseModel):
    """Base class for all brewing ingredients."""

    name = models.CharField(max_length=100)
    country_of_origin = models.CharField(
        max_length=100,
        default="",
        help_text="Country of origin",
    )
    notes = models.TextField(
        blank=True,
        default="",
        help_text="Notes for special instructions",
    )

    class Meta:
        abstract = True

    def __str__(self) -> str:
        return str(self.name)


class Hop(Ingredient):
    """Hop ingredient with alpha/beta acid content."""

    alpha_acid = models.FloatField(
        default=0.0,
        help_text="Alpha acid percentage",
    )
    beta_acid = models.FloatField(
        default=0.0,
        help_text="Beta acid percentage",
    )

    aroma = models.BooleanField(default=False)
    bittering = models.BooleanField(default=False)

    def _get_id_prefix(self) -> str:
        return "hop"


class Fermentable(Ingredient):
    name = models.CharField(max_length=100)

    fermentable_type = models.CharField(
        max_length=10,
        choices=FermentableType.choices,
        default=FermentableType.BASE_MALT,
    )

    extract_potential_ppg = models.FloatField(
        default=37.0,
        help_text="Extract potential in points per pound per gallon",
    )

    color_lovibond = models.FloatField(
        default=2.0,
        help_text="Color in degrees Lovibond",
    )

    requires_mashing = models.BooleanField(
        default=True,
        help_text="Whether this fermentable requires mashing",
    )

    def _get_id_prefix(self) -> str:
        return "frm"
