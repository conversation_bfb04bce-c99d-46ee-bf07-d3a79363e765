"""
Yeast service for the hoplogic application.

This module contains the service class for yeast-related business logic.
"""

import logging


from ..agents.tools.factory import agent_accessible
from ..di import injectable
from ..models import Yeast
from ..repositories.yeast import YeastInclusionRepository, YeastRepository

logger = logging.getLogger(__name__)


@injectable
class YeastService:
    """Service for managing yeasts."""

    def __init__(
        self,
        repository: YeastRepository,
        inclusion_repository: YeastInclusionRepository,
    ):
        logger.debug("Initializing YeastService")
        self.repository = repository
        self.inclusion_repository = inclusion_repository

    @agent_accessible()
    def get_yeast_by_id(self, yeast_id: str) -> Yeast | None:
        """Get a yeast by ID."""
        return self.repository.get_by_id(yeast_id)

    @agent_accessible()
    def search_yeasts(self, name_query: str) -> list[Yeast]:
        # TODO: make this a full text search with vector embeddings
        logger.info(f"Searching yeasts with query: {name_query}")
        return self.repository.search_by_name(name_query)

    @agent_accessible()
    def get_yeasts_within_temperature_range(
        self, min_temp: float, max_temp: float
    ) -> list[Yeast]:
        return self.repository.get_by_temperature_range(min_temp, max_temp)

    @agent_accessible()
    def get_yeasts_within_attenuation_range(
        self, min_attenuation: float, max_attenuation: float
    ) -> list[Yeast]:
        return self.repository.get_by_attenuation_range(
            min_attenuation, max_attenuation
        )
