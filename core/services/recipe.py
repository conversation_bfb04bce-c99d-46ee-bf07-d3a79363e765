import logging

from django.db import transaction

from ..agents.tools.factory import agent_accessible
from ..di import injectable
from ..models import (
    FermentableInclusion,
    Recipe,
    YeastInclusion,
)
from ..repositories import (
    BoilHopInclusionRepository,
    DryHopInclusionRepository,
    FermentableInclusionRepository,
    FermentableRepository,
    FirstWortHopInclusionRepository,
    FlameoutHopInclusionRepository,
    RecipeRepository,
    WhirlpoolHopInclusionRepository,
    YeastInclusionRepository,
)

logger = logging.getLogger(__name__)


@injectable
class RecipeService:
    """Service for managing recipes.

    This service provides methods for creating, updating, and deleting recipes.
    """

    def __init__(
        self,
        recipe_repository: RecipeRepository,
        fermentable_repository: FermentableRepository,
        fermentable_inclusion_repository: FermentableInclusionRepository,
        boil_hop_repository: BoilHopInclusionRepository,
        first_wort_repository: FirstWortHopInclusionRepository,
        flameout_repository: FlameoutHopInclusionRepository,
        whirlpool_repository: WhirlpoolHopInclusionRepository,
        dry_hop_repository: DryHopInclusionRepository,
        yeast_inclusion_repository: YeastInclusionRepository,
    ):
        logger.debug("Initializing RecipeService")
        self.recipe_repository = recipe_repository
        self.fermentable_repository = fermentable_repository
        self.fermentable_inclusion_repository = fermentable_inclusion_repository
        self.boil_hop_repository = boil_hop_repository
        self.first_wort_repository = first_wort_repository
        self.flameout_repository = flameout_repository
        self.whirlpool_repository = whirlpool_repository
        self.dry_hop_repository = dry_hop_repository
        self.yeast_inclusion_repository = yeast_inclusion_repository

    def get_recipe_by_id(self, recipe_id: str) -> Recipe | None:
        """Get a recipe by ID."""
        return self.recipe_repository.get_by_id(recipe_id)

    def get_recipe_with_inclusions(self, recipe_id: str) -> Recipe | None:
        """Get a recipe with all inclusions prefetched."""
        return self.recipe_repository.get_with_all_inclusions(recipe_id)

    @agent_accessible()
    def get_included_fermentables_by_id(
        self, recipe_id: str
    ) -> list[FermentableInclusion]:
        """Get the fermentables included in the recipe and their quantities."""
        return self.fermentable_inclusion_repository.get_by_recipe_with_fermentables(
            recipe_id
        )

    @agent_accessible()
    @transaction.atomic
    def add_fermentable_to_recipe(
        self,
        recipe_id: str,
        fermentable_id: str,
        quantity: float,
        quantity_unit: str,
        efficiency_percent: float | None = None,
    ) -> FermentableInclusion:
        """Add a fermentable to the recipe. The input is the ID of the fermentable to add, the quantity, and the quantity unit."""
        print(
            "Adding fermentable to recipe",
            recipe_id,
            fermentable_id,
            quantity,
            quantity_unit,
            efficiency_percent,
        )

        recipe = self.recipe_repository.get_by_id_or_raise(recipe_id)
        fermentable = self.fermentable_repository.get_by_id_or_raise(fermentable_id)

        # Check if fermentable is already included
        if self.fermentable_inclusion_repository.exists_for_recipe_and_fermentable(
            recipe_id, fermentable_id
        ):
            raise ValueError(
                f"Recipe already includes fermentable: {fermentable.name}. Update the existing inclusion instead."
            )

        return self.fermentable_inclusion_repository.create(
            recipe=recipe,
            fermentable=fermentable,
            quantity=quantity,
            quantity_unit=quantity_unit,
            efficiency_percent=efficiency_percent,
        )

    @agent_accessible()
    @transaction.atomic
    def remove_fermentable_from_recipe(self, recipe_id: str, inclusion_id: str) -> str:
        """Remove a fermentable from the recipe. The input is the ID of the fermentable inclusion to remove."""
        fermentable_inclusion = (
            self.fermentable_inclusion_repository.get_by_id_or_raise(inclusion_id)
        )

        # Verify the inclusion belongs to the specified recipe
        if fermentable_inclusion.recipe.id != recipe_id:
            raise ValueError(
                f"Fermentable inclusion {inclusion_id} does not belong to recipe {recipe_id}"
            )

        fermentable_name = fermentable_inclusion.fermentable.name
        self.fermentable_inclusion_repository.delete(fermentable_inclusion)
        return f"Removed {fermentable_name} from the recipe."

    @agent_accessible()
    @transaction.atomic
    def update_fermentable_in_recipe(
        self,
        recipe_id: str,
        inclusion_id: str,
        quantity: float | None = None,
        quantity_unit: str | None = None,
        efficiency_percent: float | None = None,
    ) -> FermentableInclusion:
        """Update a fermentable in the recipe. The input is the ID of the fermentable inclusion to update, the quantity, and the quantity unit."""
        fermentable_inclusion = (
            self.fermentable_inclusion_repository.get_by_id_or_raise(inclusion_id)
        )

        # Verify the inclusion belongs to the specified recipe
        if fermentable_inclusion.recipe.id != recipe_id:
            raise ValueError(
                f"Fermentable inclusion {inclusion_id} does not belong to recipe {recipe_id}"
            )

        # Update fields if provided
        update_data = {}
        if quantity is not None:
            update_data["quantity"] = quantity
        if quantity_unit is not None:
            update_data["quantity_unit"] = quantity_unit
        if efficiency_percent is not None:
            update_data["efficiency_percent"] = efficiency_percent

        return self.fermentable_inclusion_repository.update(
            fermentable_inclusion, **update_data
        )

    @agent_accessible()
    def get_recipe_hop_inclusions(self, recipe_id: str) -> dict:
        """Get all hop inclusions for the recipe, organized by type."""
        return {
            "boil_hops": self.boil_hop_repository.get_by_recipe_with_hops(recipe_id),
            "first_wort_hops": self.first_wort_repository.get_by_recipe_with_hops(
                recipe_id
            ),
            "flameout_hops": self.flameout_repository.get_by_recipe_with_hops(
                recipe_id
            ),
            "whirlpool_hops": self.whirlpool_repository.get_by_recipe_with_hops(
                recipe_id
            ),
            "dry_hops": self.dry_hop_repository.get_by_recipe_with_hops(recipe_id),
        }

    @agent_accessible()
    def get_recipe_yeast_inclusions(self, recipe_id: str) -> list[YeastInclusion]:
        """Get the yeast inclusions for the recipe."""
        return self.yeast_inclusion_repository.get_by_recipe_with_yeast(recipe_id)
