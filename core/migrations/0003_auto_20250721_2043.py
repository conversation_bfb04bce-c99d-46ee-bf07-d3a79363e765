# Generated by Django 5.2.4 on 2025-07-21 20:43

from django.db import migrations


def create_default_beer_style(apps, schema_editor):
    """Create a default beer style for existing recipes."""
    BeerStyle = apps.get_model('core', 'BeerStyle')
    Recipe = apps.get_model('core', 'Recipe')

    # Create the default beer style
    default_style, created = BeerStyle.objects.get_or_create(
        name="Custom Style",
        defaults={
            'description': "A custom beer style with no specific guidelines. Perfect for experimental and unique recipes.",
        }
    )

    # Assign the default style to all existing recipes that don't have a beer style
    recipes_without_style = Recipe.objects.filter(beer_style__isnull=True)
    for recipe in recipes_without_style:
        recipe.beer_style = default_style
        recipe.save()


def reverse_default_beer_style(apps, schema_editor):
    """Remove the default beer style (reverse migration)."""
    BeerStyle = apps.get_model('core', 'BeerStyle')
    Recipe = apps.get_model('core', 'Recipe')

    # Remove beer style from all recipes that have the default style
    try:
        default_style = BeerStyle.objects.get(name="Custom Style")
        Recipe.objects.filter(beer_style=default_style).update(beer_style=None)
        default_style.delete()
    except BeerStyle.DoesNotExist:
        pass  # Style doesn't exist, nothing to reverse


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_beerstyle_recipe_beer_style"),
    ]

    operations = [
        migrations.RunPython(
            create_default_beer_style,
            reverse_default_beer_style,
        ),
    ]
