"""
Core dependency injection container implementation.

This module provides a minimal, singleton-based DI container that supports
type-safe dependency resolution with circular dependency detection.
"""

import inspect
import logging
import functools
from abc import ABC, abstractmethod
from typing import Dict, TypeVar, Callable, Optional, Generic, Type
from unittest.mock import MagicMock

logger = logging.getLogger(__name__)

T = TypeVar("T")


def annotated_parameters(func: Callable) -> Dict[str, type]:
    sig = inspect.signature(func)
    return {
        name: param.annotation
        for name, param in sig.parameters.items()
        if param.annotation != inspect.Parameter.empty
    }


class Resolvable(Generic[T], ABC):
    @abstractmethod
    def get(self) -> T: ...


class Mock(Resolvable[T]):
    def __init__(self, type: Type[T]):
        self.type = type
        self.mock = MagicMock(spec=type)

    def get(self) -> T:
        return self.mock


class Singleton(Resolvable[T]):
    def __init__(self, type: Type[T]):
        self.type = type
        self.instance = None
        self.resolving = False
        self.dependencies = annotated_parameters(type)

    def make(self) -> T:
        if self.resolving:
            raise CircularDependencyError(self.type, dep_type)

        self.resolving = True
        container = Container.instance()
        deps = {
            name: container.resolve(dep_type)
            for name, dep_type in self.dependencies.items()
        }
        self.resolving = False
        return self.type(**deps)

    def get(self) -> T:
        if self.instance is None:
            self.instance = self.make()
        return self.instance


class CircularDependencyError(Exception):
    """Raised when a circular dependency is detected."""

    def __init__(self, type_being_resolved: type, dependency: type):
        super().__init__(
            f"Circular dependency detected: {type_being_resolved.__name__} -> {dependency.__name__}"
        )
        self.type_being_resolved = type_being_resolved
        self.dependency = dependency


class UnmetDependencyError(Exception):
    """Raised when a dependency is not met."""

    def __init__(self, type: Type):
        super().__init__(f"Unmet dependency: {type.__name__}")
        self.type = type


class Container:
    _instance: Optional["Container"] = None

    @classmethod
    def instance(cls) -> "Container":
        if cls._instance is None:
            cls._instance = Container()
        return cls._instance

    def __init__(self):
        self.resolveables: Dict[type, Resolvable] = {}
        self.auto_inject_mocks = False

    def clear(self):
        self.resolveables = {}

    def register(self, interface: Type[T], implementation: Type[T]):
        self.resolveables[interface] = Singleton(implementation)

    def resolve(self, type: Type[T]) -> T:
        # If we do not have a resolvable for this dependency,
        # automatically register it as a mock if auto-inject is enabled
        # Otherwise, raise an exception because we cannot fill the request.
        # The application has been misconfigured.
        if type not in self.resolveables:
            if self.auto_inject_mocks:
                self.resolveables[type] = Mock(type)
            else:
                raise UnmetDependencyError(type)

        return self.resolveables[type].get()


def injectable(cls: type[T] = None, *, interface: type[T] = None) -> type[T]:
    """
    Mark a class as injectable for automatic dependency resolution.

    Classes marked with @injectable can be automatically resolved by the
    DI container by inspecting their constructor parameters.

    Example:
        @injectable
        class MyService:
            def __init__(self, repo: MyRepository):
                self.repo = repo

    You can also specify an interface to register the implementation against:
        @injectable(interface=ServiceInterface)
        class ConcreteService:
            ...
    """

    def decorator(cls: type[T]) -> type[T]:
        Container.instance().register(interface or cls, cls)
        return cls

    if cls is None:
        return decorator
    else:
        return decorator(cls)


def inject(func: Callable[..., T]) -> Callable[..., T]:
    """
    Inject dependencies into a function or method.

    This decorator inspects the function signature and automatically
    resolves dependencies from the DI container.

    Example:
        @inject
        def my_function(service: MyService) -> str:
            return service.do_something()
    """
    sig = inspect.signature(func)

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        container = Container.instance()
        bound = sig.bind_partial(*args, **kwargs)

        # Resolve missing parameters using the container.
        for param_name, param_type in annotated_parameters(func).items():
            if param_name not in bound.arguments:
                dependency = container.resolve(param_type)
                bound.arguments[param_name] = dependency
                logger.debug("Injected %s into %s", param_type.__name__, func.__name__)

        return func(*bound.args, **bound.kwargs)

    return wrapper
