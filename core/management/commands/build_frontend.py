"""
Management command to build the Vue.js frontend.
"""

import subprocess
import sys
from pathlib import Path

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings


class Command(BaseCommand):
    help = "Build the Vue.js frontend for production"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dev",
            action="store_true",
            help="Run in development mode with hot reload",
        )

    def handle(self, *args, **options):
        project_root = Path(settings.BASE_DIR)
        
        # Check if package.json exists
        package_json = project_root / "package.json"
        if not package_json.exists():
            raise CommandError("package.json not found. Run this from the project root.")

        try:
            if options["dev"]:
                self.stdout.write("Starting Vue.js development server...")
                subprocess.run(["npm", "run", "dev"], cwd=project_root, check=True)
            else:
                self.stdout.write("Installing npm dependencies...")
                subprocess.run(["npm", "install"], cwd=project_root, check=True)
                
                self.stdout.write("Building Vue.js frontend...")
                subprocess.run(["npm", "run", "build"], cwd=project_root, check=True)
                
                self.stdout.write(
                    self.style.SUCCESS("Frontend built successfully!")
                )
                
        except subprocess.CalledProcessError as e:
            raise CommandError(f"Build failed: {e}")
        except FileNotFoundError:
            raise CommandError(
                "npm not found. Please install Node.js and npm first."
            )
