"""
Tests for FermentableInclusionRepository.

This module contains comprehensive tests for the FermentableInclusionRepository class,
testing all fermentable inclusion-specific operations and queries.
"""

import pytest

from core.repositories.fermentable_inclusion import FermentableInclusionRepository
from core.models import FermentableInclusion
from tests.factories import FermentableInclusionFactory, RecipeFactory, FermentableFactory
from .base import BaseRepositoryTest


class TestFermentableInclusionRepository(BaseRepositoryTest):
    """Test FermentableInclusionRepository functionality."""

    repository_class = FermentableInclusionRepository
    model_class = FermentableInclusion
    factory_class = FermentableInclusionFactory

    def get_create_data(self) -> dict:
        """Get data for creating fermentable inclusion instances."""
        recipe = RecipeFactory.create_in_db()
        fermentable = FermentableFactory.create_in_db()
        return {
            'recipe': recipe,
            'fermentable': fermentable,
            'quantity': 10.0,
            'quantity_unit': 'POUNDS',
            'efficiency_percent': 75.0,
            'notes': 'Test fermentable inclusion notes',
        }

    def get_update_data(self) -> dict:
        """Get data for updating fermentable inclusion instances."""
        return {
            'quantity': 12.0,
            'efficiency_percent': 80.0,
            'notes': 'Updated fermentable inclusion notes',
        }

    @pytest.mark.django_db
    def test_get_by_recipe(self):
        """Test getting fermentable inclusions by recipe."""
        # Arrange
        recipe1 = RecipeFactory.create_in_db(name="Recipe 1")
        recipe2 = RecipeFactory.create_in_db(name="Recipe 2")

        inclusion1 = FermentableInclusionFactory.create_in_db(recipe=recipe1)
        inclusion2 = FermentableInclusionFactory.create_in_db(recipe=recipe1)
        inclusion3 = FermentableInclusionFactory.create_in_db(recipe=recipe2)

        # Act
        result = self.repository.get_by_recipe(recipe1.id)

        # Assert
        assert len(result) == 2
        result_ids = [i.id for i in result]
        assert inclusion1.id in result_ids
        assert inclusion2.id in result_ids
        assert inclusion3.id not in result_ids

    @pytest.mark.django_db
    def test_get_by_recipe_no_inclusions(self):
        """Test getting fermentable inclusions by recipe when none exist."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Empty Recipe")

        # Act
        result = self.repository.get_by_recipe(recipe.id)

        # Assert
        assert result == []

    @pytest.mark.django_db
    def test_get_by_recipe_with_fermentables(self):
        """Test getting fermentable inclusions with fermentable data prefetched."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Recipe with Fermentables")
        fermentable = FermentableFactory.create_in_db(name="Test Fermentable")
        inclusion = FermentableInclusionFactory.create_in_db(recipe=recipe, fermentable=fermentable)

        # Act
        result = self.repository.get_by_recipe_with_fermentables(recipe.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == inclusion.id
        assert result[0].fermentable.name == "Test Fermentable"
        # Verify fermentable is prefetched (this would normally cause a DB query without prefetch)
        assert result[0].fermentable.id == fermentable.id

    @pytest.mark.django_db
    def test_get_by_recipe_with_fermentables_no_inclusions(self):
        """Test getting fermentable inclusions with fermentable data when none exist."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Empty Recipe")

        # Act
        result = self.repository.get_by_recipe_with_fermentables(recipe.id)

        # Assert
        assert result == []

    @pytest.mark.django_db
    def test_get_by_recipe_and_fermentable_found(self):
        """Test getting a specific fermentable inclusion by recipe and fermentable."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        fermentable1 = FermentableFactory.create_in_db(name="Fermentable 1")
        fermentable2 = FermentableFactory.create_in_db(name="Fermentable 2")

        inclusion1 = FermentableInclusionFactory.create_in_db(recipe=recipe, fermentable=fermentable1)
        inclusion2 = FermentableInclusionFactory.create_in_db(recipe=recipe, fermentable=fermentable2)

        # Act
        result = self.repository.get_by_recipe_and_fermentable(recipe.id, fermentable1.id)

        # Assert
        assert result is not None
        assert result.id == inclusion1.id
        assert result.recipe.id == recipe.id
        assert result.fermentable.id == fermentable1.id

    @pytest.mark.django_db
    def test_get_by_recipe_and_fermentable_not_found(self):
        """Test getting a specific fermentable inclusion when it doesn't exist."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        fermentable = FermentableFactory.create_in_db(name="Test Fermentable")

        # Act
        result = self.repository.get_by_recipe_and_fermentable(recipe.id, fermentable.id)

        # Assert
        assert result is None

    @pytest.mark.django_db
    def test_exists_for_recipe_and_fermentable_true(self):
        """Test checking if fermentable inclusion exists when it does."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        fermentable = FermentableFactory.create_in_db(name="Test Fermentable")
        FermentableInclusionFactory.create_in_db(recipe=recipe, fermentable=fermentable)

        # Act
        result = self.repository.exists_for_recipe_and_fermentable(recipe.id, fermentable.id)

        # Assert
        assert result is True

    @pytest.mark.django_db
    def test_exists_for_recipe_and_fermentable_false(self):
        """Test checking if fermentable inclusion exists when it doesn't."""
        # Arrange
        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        fermentable = FermentableFactory.create_in_db(name="Test Fermentable")

        # Act
        result = self.repository.exists_for_recipe_and_fermentable(recipe.id, fermentable.id)

        # Assert
        assert result is False

    @pytest.mark.django_db
    def test_get_base_malt_inclusions(self):
        """Test getting base malt fermentable inclusions for a recipe."""
        # Arrange
        from core.models import FermentableType

        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        base_malt = FermentableFactory.create_in_db(
            name="Base Malt",
            fermentable_type=FermentableType.BASE_MALT
        )
        specialty_malt = FermentableFactory.create_in_db(
            name="Specialty Malt",
            fermentable_type=FermentableType.SPECIALTY_MALT
        )

        base_inclusion = FermentableInclusionFactory.create_in_db(recipe=recipe, fermentable=base_malt)
        specialty_inclusion = FermentableInclusionFactory.create_in_db(recipe=recipe, fermentable=specialty_malt)

        # Act
        result = self.repository.get_base_malt_inclusions(recipe.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == base_inclusion.id
        assert result[0].fermentable.fermentable_type == FermentableType.BASE_MALT

    @pytest.mark.django_db
    def test_get_specialty_inclusions(self):
        """Test getting specialty malt fermentable inclusions for a recipe."""
        # Arrange
        from core.models import FermentableType

        recipe = RecipeFactory.create_in_db(name="Test Recipe")
        base_malt = FermentableFactory.create_in_db(
            name="Base Malt",
            fermentable_type=FermentableType.BASE_MALT
        )
        specialty_malt = FermentableFactory.create_in_db(
            name="Specialty Malt",
            fermentable_type=FermentableType.SPECIALTY_MALT
        )
        crystal_malt = FermentableFactory.create_in_db(
            name="Crystal Malt",
            fermentable_type=FermentableType.CRYSTAL_CARAMEL
        )

        base_inclusion = FermentableInclusionFactory.create_in_db(recipe=recipe, fermentable=base_malt)
        specialty_inclusion = FermentableInclusionFactory.create_in_db(recipe=recipe, fermentable=specialty_malt)
        crystal_inclusion = FermentableInclusionFactory.create_in_db(recipe=recipe, fermentable=crystal_malt)

        # Act
        result = self.repository.get_specialty_inclusions(recipe.id)

        # Assert
        assert len(result) == 2
        result_ids = [i.id for i in result]
        assert specialty_inclusion.id in result_ids
        assert crystal_inclusion.id in result_ids
        assert base_inclusion.id not in result_ids

    @pytest.mark.django_db
    def test_repository_inherits_base_functionality(self):
        """Test that FermentableInclusionRepository inherits all base repository functionality."""
        # Test that all base methods are available
        assert hasattr(self.repository, 'get_by_id')
        assert hasattr(self.repository, 'get_by_id_or_raise')
        assert hasattr(self.repository, 'get_all')
        assert hasattr(self.repository, 'filter')
        assert hasattr(self.repository, 'create')
        assert hasattr(self.repository, 'update')
        assert hasattr(self.repository, 'delete')
        assert hasattr(self.repository, 'exists')
        assert hasattr(self.repository, 'count')

        # Test that fermentable inclusion-specific methods are available
        assert hasattr(self.repository, 'get_by_recipe')
        assert hasattr(self.repository, 'get_by_recipe_with_fermentables')
        assert hasattr(self.repository, 'get_by_recipe_and_fermentable')
        assert hasattr(self.repository, 'exists_for_recipe_and_fermentable')
        assert hasattr(self.repository, 'get_base_malt_inclusions')
        assert hasattr(self.repository, 'get_specialty_inclusions')

    @pytest.mark.django_db
    def test_model_class_is_fermentable_inclusion(self):
        """Test that repository model class is FermentableInclusion."""
        assert self.repository.model_class == FermentableInclusion
