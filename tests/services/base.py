"""
Base test utilities for service layer tests.

This module contains base classes and utilities for testing service classes
with proper mocking of repository dependencies.
"""

import pytest
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, List, Optional

from core.models import (
    Recipe, Hop, Fermentable, FermentableType, Yeast, YeastType, YeastForm,
    BoilHopInclusion, FirstWortHopInclusion, FlameoutHopInclusion,
    WhirlpoolHopInclusion, DryHopInclusion, FermentableInclusion, YeastInclusion
)


class MockRepositoryFactory:
    """Factory for creating mock repository objects with common behavior."""

    @staticmethod
    def create_base_repository_mock() -> <PERSON>ck:
        """Create a mock repository with all base repository methods."""
        mock = Mock()

        # Base repository methods
        mock.get_by_id.return_value = None
        mock.get_by_id_or_raise.side_effect = lambda id: Mock(id=id)
        mock.get_all.return_value = []
        mock.filter.return_value = []
        mock.create.side_effect = lambda **kwargs: Mock(**kwargs)
        mock.update.side_effect = lambda obj, **kwargs: <PERSON>ck(**{**obj.__dict__, **kwargs})
        mock.delete.return_value = None
        mock.exists.return_value = False
        mock.count.return_value = 0

        return mock

    @staticmethod
    def create_recipe_repository_mock() -> Mock:
        """Create a mock RecipeRepository."""
        mock = MockRepositoryFactory.create_base_repository_mock()

        # Recipe-specific methods
        mock.get_with_all_inclusions.return_value = None
        mock.get_by_user.return_value = []
        mock.search_by_name.return_value = []

        return mock

    @staticmethod
    def create_fermentable_repository_mock() -> Mock:
        """Create a mock FermentableRepository."""
        mock = MockRepositoryFactory.create_base_repository_mock()

        # Fermentable-specific methods
        mock.get_by_type.return_value = []
        mock.get_base_malts.return_value = []
        mock.get_specialty_malts.return_value = []
        mock.search_by_name.return_value = []
        mock.get_by_country.return_value = []
        mock.get_requiring_mashing.return_value = []
        mock.get_extracts.return_value = []

        return mock

    @staticmethod
    def create_hop_repository_mock() -> Mock:
        """Create a mock HopRepository."""
        mock = MockRepositoryFactory.create_base_repository_mock()

        # Hop-specific methods
        mock.get_aroma_hops.return_value = []
        mock.get_bittering_hops.return_value = []
        mock.get_dual_purpose_hops.return_value = []
        mock.search_by_name.return_value = []
        mock.get_by_country.return_value = []
        mock.get_high_alpha_hops.return_value = []
        mock.get_low_alpha_hops.return_value = []

        return mock

    @staticmethod
    def create_yeast_repository_mock() -> Mock:
        """Create a mock YeastRepository."""
        mock = MockRepositoryFactory.create_base_repository_mock()

        # Yeast-specific methods
        mock.get_by_type.return_value = []
        mock.get_ale_yeasts.return_value = []
        mock.get_lager_yeasts.return_value = []
        mock.get_by_form.return_value = []
        mock.get_by_laboratory.return_value = []
        mock.search_by_name.return_value = []
        mock.get_by_temperature_range.return_value = []
        mock.get_by_attenuation_range.return_value = []

        return mock

    @staticmethod
    def create_hop_inclusion_repository_mock() -> Mock:
        """Create a mock hop inclusion repository (works for all hop inclusion types)."""
        mock = MockRepositoryFactory.create_base_repository_mock()

        # Hop inclusion-specific methods
        mock.get_by_recipe.return_value = []
        mock.get_by_recipe_with_hops.return_value = []
        mock.get_by_hop.return_value = []

        return mock

    @staticmethod
    def create_fermentable_inclusion_repository_mock() -> Mock:
        """Create a mock FermentableInclusionRepository."""
        mock = MockRepositoryFactory.create_base_repository_mock()

        # Fermentable inclusion-specific methods
        mock.get_by_recipe.return_value = []
        mock.get_by_recipe_with_fermentables.return_value = []
        mock.get_by_fermentable.return_value = []
        mock.exists_for_recipe_and_fermentable.return_value = False

        return mock

    @staticmethod
    def create_yeast_inclusion_repository_mock() -> Mock:
        """Create a mock YeastInclusionRepository."""
        mock = MockRepositoryFactory.create_base_repository_mock()

        # Yeast inclusion-specific methods
        mock.get_by_recipe.return_value = []
        mock.get_by_recipe_with_yeast.return_value = []
        mock.get_by_yeast.return_value = []

        return mock


class BaseServiceTest:
    """Base class for service tests with common utilities."""

    # Override in subclasses
    service_class = None

    def setup_method(self):
        """Set up test method - override in subclasses to inject mocks."""
        pass

    def create_mock_recipe(self, **kwargs) -> Mock:
        """Create a mock recipe object."""
        defaults = {
            'id': 'rec_test123',
            'name': 'Test Recipe',
            'batch_size_gallons': 5.0,
            'target_original_gravity': 1.050,
            'target_final_gravity': 1.010,
            'target_abv': 5.0,
            'target_ibu': 30.0,
            'target_srm': 8.0,
            'description': 'Test recipe description'
        }
        defaults.update(kwargs)
        return Mock(**defaults)

    def create_mock_hop(self, **kwargs) -> Mock:
        """Create a mock hop object."""
        defaults = {
            'id': 'hop_test123',
            'name': 'Test Hop',
            'alpha_acid_percent': 10.0,
            'beta_acid_percent': 4.0,
            'aroma': True,
            'bittering': True,
            'country_of_origin': 'US',
            'description': 'Test hop description'
        }
        defaults.update(kwargs)
        return Mock(**defaults)

    def create_mock_fermentable(self, **kwargs) -> Mock:
        """Create a mock fermentable object."""
        defaults = {
            'id': 'fer_test123',
            'name': 'Test Fermentable',
            'fermentable_type': FermentableType.BASE_MALT,
            'extract_potential': 1.037,
            'color_lovibond': 2.0,
            'requires_mashing': True,
            'country_of_origin': 'US',
            'description': 'Test fermentable description'
        }
        defaults.update(kwargs)
        return Mock(**defaults)

    def create_mock_yeast(self, **kwargs) -> Mock:
        """Create a mock yeast object."""
        defaults = {
            'id': 'yst_test123',
            'name': 'Test Yeast',
            'laboratory': 'Test Labs',
            'product_id': 'T001',
            'yeast_type': YeastType.ALE,
            'yeast_form': YeastForm.DRY,
            'min_temperature_fahrenheit': 60.0,
            'max_temperature_fahrenheit': 75.0,
            'attenuation_percent': 75.0,
            'flocculation': 'MEDIUM',
            'alcohol_tolerance_percent': 12.0,
            'description': 'Test yeast description',
            'temperature_range_display': '60-75°F',
            'get_yeast_type_display.return_value': 'Ale Yeast',
            'get_yeast_form_display.return_value': 'Dry',
            'get_flocculation_display.return_value': 'Medium'
        }
        defaults.update(kwargs)
        return Mock(**defaults)

    def create_mock_hop_inclusion(self, inclusion_type='boil', **kwargs) -> Mock:
        """Create a mock hop inclusion object."""
        defaults = {
            'id': f'{inclusion_type[:3]}_test123',
            'recipe': self.create_mock_recipe(),
            'hop': self.create_mock_hop(),
            'quantity': 1.0,
            'quantity_unit': 'OUNCES'
        }

        # Add type-specific defaults
        if inclusion_type == 'boil':
            defaults.update({
                'time_minutes': 60,
                'is_bittering_addition.return_value': True,
                'is_aroma_addition.return_value': False,
                'ibu_contribution.return_value': 25.0
            })
        elif inclusion_type == 'dry':
            defaults.update({
                'days': 3,
                'is_aroma_addition.return_value': True,
                'ibu_contribution.return_value': 0.0
            })
        elif inclusion_type == 'whirlpool':
            defaults.update({
                'temperature_fahrenheit': 180.0,
                'time_minutes': 20,
                'is_aroma_addition.return_value': True,
                'ibu_contribution.return_value': 5.0
            })

        defaults.update(kwargs)
        return Mock(**defaults)

    def create_mock_fermentable_inclusion(self, **kwargs) -> Mock:
        """Create a mock fermentable inclusion object."""
        defaults = {
            'id': 'fei_test123',
            'recipe': self.create_mock_recipe(),
            'fermentable': self.create_mock_fermentable(),
            'quantity': 10.0,
            'quantity_unit': 'POUNDS',
            'efficiency_percent': 75.0,
            'quantity_in_pounds.return_value': 10.0,
            'gravity_points.return_value': 37.0,
            'srm_contribution.return_value': 20.0
        }
        defaults.update(kwargs)
        return Mock(**defaults)

    def create_mock_yeast_inclusion(self, **kwargs) -> Mock:
        """Create a mock yeast inclusion object."""
        defaults = {
            'id': 'ysi_test123',
            'recipe': self.create_mock_recipe(),
            'yeast': self.create_mock_yeast(),
            'quantity': 1.0,
            'quantity_unit': 'PACKET',
            'starter_made': False,
            'starter_size_ml': None,
            'notes': ''
        }
        defaults.update(kwargs)
        return Mock(**defaults)


# Pytest fixtures for service testing
@pytest.fixture
def mock_recipe_repository():
    """Mock RecipeRepository fixture."""
    return MockRepositoryFactory.create_recipe_repository_mock()


@pytest.fixture
def mock_fermentable_repository():
    """Mock FermentableRepository fixture."""
    return MockRepositoryFactory.create_fermentable_repository_mock()


@pytest.fixture
def mock_hop_repository():
    """Mock HopRepository fixture."""
    return MockRepositoryFactory.create_hop_repository_mock()


@pytest.fixture
def mock_yeast_repository():
    """Mock YeastRepository fixture."""
    return MockRepositoryFactory.create_yeast_repository_mock()


@pytest.fixture
def mock_hop_inclusion_repository():
    """Mock hop inclusion repository fixture."""
    return MockRepositoryFactory.create_hop_inclusion_repository_mock()


@pytest.fixture
def mock_fermentable_inclusion_repository():
    """Mock FermentableInclusionRepository fixture."""
    return MockRepositoryFactory.create_fermentable_inclusion_repository_mock()


@pytest.fixture
def mock_yeast_inclusion_repository():
    """Mock YeastInclusionRepository fixture."""
    return MockRepositoryFactory.create_yeast_inclusion_repository_mock()
